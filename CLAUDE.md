# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Quick Start Commands

### Environment Setup
```bash
# Initial setup
chmod +x setup_dev_env.sh
./setup_dev_env.sh

# Configure environment
cp env.example .env
# Edit .env with Feishu app credentials and database connections

# Start database
docker-compose up -d mysql
```

### Daily Development Commands
```bash
# Start/restart application
./restart.sh --debug              # Debug mode with verbose logging
./restart.sh                      # Production mode

# View logs
./restart.sh && tail -f logs/chat_bi_mysql.log

# Dependency management
uv sync                           # Sync dependencies
uv pip install <package>          # Install new package
```

### Code Quality Commands
```bash
# Format code
uv run black src/ tests/

# Lint code
uv run flake8 src/ tests/

# Type checking
uv run mypy src/

# Run tests (when available)
uv run pytest tests/
```

## Architecture Overview

### Core Architecture Pattern: Agent-as-Tool
ChatBI uses a sophisticated **Agent-as-Tool** architecture where specialized agents are registered as tools that a coordinator agent can invoke based on user queries.

### Key Components
- **CoordinatorBot**: Routes user queries to appropriate specialized agents
- **DataFetcherBot**: Executes SQL queries and data analysis
- **Specialized Agents**: Sales analytics, warehouse management, KPI tracking
- **Dual Database**: ChatBI system DB (3307) + Business data warehouse (3306)

### Request Flow
1. User query → CoordinatorBot (LLM)
2. Coordinator analyzes query → Selects appropriate specialized agent
3. Specialized agent executes query → Returns results
4. Coordinator formats response → Returns to user

### Directory Structure
```
src/
├── api/                     # REST endpoints (auth, query, dashboard)
├── services/
│   ├── agent/              # Agent system (coordinator, data fetcher)
│   ├── auth/               # JWT + Feishu OAuth2.0
│   ├── feishu/             # Feishu bot integration
│   └── xianmudb/           # Business DB operations
├── static/                 # React frontend
├── templates/              # HTML templates
└── utils/                  # Common utilities

resources/
├── data_fetcher_bot_config/ # Agent YAML configs
├── prompt/                 # System prompts
└── tables_ddl/             # Database schemas
```

## Agent Development

### Adding New Specialized Agents
1. Create YAML config in `resources/data_fetcher_bot_config/`
2. Add system prompt in `resources/prompt/`
3. Register in `coordinator_bot.yml` under `agent_tools`
4. Add custom tools to `src/services/agent/tools/` if needed

### Agent Configuration Format
```yaml
agent_name: sales_analytics
model_provider: openrouter
model: anthropic/claude-sonnet-4
tools:
  - name: execute_sql_query
agent_tools:
  - name: sales_order_analytics
need_system_prompt: true
agent_description: sales_analytics.md
```

## Database Operations

### Connection Management
- **ChatBI DB**: System data (users, sessions, history) - port 3307
- **Business DB**: Analytics data (orders, merchants, reports) - port 3306
- Connection pooling via `src/db/connection.py`

### Safe Query Pattern
```python
from src.services.xianmudb.query_service import execute_business_query_async

result = await execute_business_query_async(
    "SELECT * FROM orders WHERE id = %s", 
    params=(order_id,)
)
```

## API Development

### Adding New Endpoints
1. Create new file in `src/api/`
2. Use `@login_required` decorator for protected routes
3. Register in `src/api/__init__.py`
4. Follow existing JSON response patterns

### Available Endpoints
- `/api/query` - Main query endpoint
- `/api/auth/*` - Authentication
- `/api/dashboard/*` - Analytics dashboard
- `/api/history/*` - Conversation history
- `/feishu/webhook` - Feishu bot webhook

## Testing

### Manual Testing Commands
```bash
# Test agent query
curl -X POST http://localhost:5700/api/query \
  -H "Content-Type: application/json" \
  -d '{"message": "展示月度销售趋势"}'

# Test Feishu webhook
curl -X POST http://localhost:5700/feishu/webhook \
  -H "Content-Type: application/json" \
  -d '{"challenge": "test"}'
```

### Test Structure
- Tests located in `tests/` directory
- Organized by service modules
- Use pytest framework

## Configuration

### Key Environment Variables
- `APPLICATION_ROOT`: URL path prefix
- `ENABLE_BOT_MESSAGE_PROCESSING`: Enable/disable Feishu bot
- `FEISHU_APP_ID`, `FEISHU_APP_SECRET`: Feishu credentials
- Database connection strings for both ChatBI and business DBs

### Ports
- **App**: 5700
- **ChatBI DB**: 3307 (mapped from container 3306)
- **Business DB**: 3306

## Troubleshooting

### Common Issues
- **Authentication failures**: Check JWT token validity and session expiration
- **Agent timeouts**: Monitor DB connection pool status, optimize SQL queries
- **Feishu no response**: Check WebSocket connection, verify Feishu app config
- **Import errors**: Ensure virtual environment is activated and dependencies synced

### Debug Mode
```bash
# Enable debug logging
./restart.sh --debug

# Check logs
tail -f logs/chat_bi_mysql.log
```

## Development Workflow

### 1. Setup Environment
```bash
./setup_dev_env.sh
source .venv/bin/activate
```

### 2. Make Changes
- Follow SOLID, DRY, KISS principles
- Use type hints
- Add appropriate logging
- **重要：所有代码注释必须使用中文编写**
- **重要：git提交信息必须使用中文描述**

### 3. Test Changes
```bash
uv run black src/
uv run flake8 src/
uv run mypy src/
./restart.sh --debug
```

### 4. Commit Changes
```bash
git add .
git commit -m "feat(scope): 中文描述"
git push
```
- 描述本次提交的具体内容，不得用抽象的、笼统的描述，比如"修复了xxx"、"优化了xxx"等。
- 描述本次改动相对于上一次提交的具体代码差异。
- 描述新代码的核心逻辑、功能、实现方式等。

### 中文优先原则
- **代码注释**：所有函数、类、复杂业务逻辑的注释必须使用中文
- **提交信息**：git commit message必须使用中文，格式为：`类型(范围): 中文描述`
- **文档字符串**：Python docstring应使用中文编写
- **变量命名**：虽然代码变量保持英文，但注释必须解释清楚中文含义