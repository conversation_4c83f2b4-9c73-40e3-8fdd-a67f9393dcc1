SELECT 
    o.m_id 客户ID,
    o.order_no,
    o.order_time as 下单时间,
    o.总GMV,
    case when o.status = 2 then '待配送' when o.status = 3 then '待收货' when o.status = 6 then '已收货' end as order_status,
    o.鲜果GMV,o.乳品GMV,o.非乳品GMV,
    f.id as follow_up_record_id,
    f.add_time as 拜访时间,
    f.admin_id BD_ID,
    f.admin_name BD名字,
    f.follow_up_way 拜访方式,
    case f.visit_objective when 0 then '拉新' when 1 then '催月活' when 2 then '客户维护' when 3 then '拓品' when 4 then '售后处理' when 5 then '催省心送' else '其他' end as 拜访目的,
    TIMESTAMPDIFF(DAY, f.add_time, o.order_time) as 拜访到下单间隔天数
FROM (
    SELECT od.`order_no`,od.`order_time`,od.`m_id`,od.`status`,`od`.`total_price` as 总GMV
      ,sum(case when c.`type` = 4 then oi.`price` * oi.`amount` end) as 鲜果GMV
      ,sum(case when c.`type` = 2 then oi.`price` * oi.`amount` end) as 乳品GMV
      ,sum(case when c.`type` in (1,3) then oi.`price` * oi.`amount` end) as 非乳品GMV
    FROM orders od
    JOIN `order_item` oi ON od.`order_no` = oi.`order_no`
    JOIN `category` c ON oi.`category_id` = c.`id` 
    WHERE od.order_time >= DATE_FORMAT(date_sub(current_date(), INTERVAL 2 MONTH), '%Y-%m-01')
    AND `m_id` in (select m_id from `follow_up_relation` WHERE `admin_name` in ('宋懿航', '李梦婷', '白津源', '冯柠柠'))
    AND od.status IN (2,3,6)  -- 只统计有效订单：待配送、待收货、已收货
    AND oi.`status` IN (2,3,6)  -- 只统计有效订单：待配送、待收货、已收货
    GROUP BY 1,2,3,4,5
) o
INNER JOIN (
    SELECT * FROM follow_up_record 
    WHERE add_time >= DATE_FORMAT(date_sub(current_date(), INTERVAL 2 MONTH), '%Y-%m-01')
    AND status = 1  -- 已跟进状态
    AND admin_id IN (
        SELECT bd_id as admin_id
        FROM crm_bd_org 
        WHERE bd_name IN ('宋懿航', '李梦婷', '白津源', '冯柠柠')
    )
) f ON o.m_id = f.m_id
WHERE 
    o.order_time >= f.add_time
    AND f.add_time = (
        SELECT MAX(f2.add_time)
        FROM follow_up_record f2
        WHERE f2.m_id = o.m_id
        AND f2.add_time <= o.order_time
        AND f2.add_time >= DATE_FORMAT(date_sub(current_date(), INTERVAL 2 MONTH), '%Y-%m-01')
        AND f2.status = 1
        AND f2.admin_id IN (
            SELECT bd_id as admin_id
            FROM crm_bd_org 
            WHERE bd_name IN ('宋懿航', '李梦婷', '白津源', '冯柠柠')
        )
    )