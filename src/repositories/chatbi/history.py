"""
Chat history repository module.

This module provides data access functions for managing chatbot history records.
"""

from datetime import datetime
import json
import re
from typing import List, Dict, Any, Optional
from mysql.connector import Error

from src.utils.logger import logger
from src.db.connection import execute_db_query, get_db_connection

def extract_agent_name_from_logs(logs: Optional[str]) -> str:
    """
    Extract agent name from logs field (for backward compatibility).

    在Agent as Tool架构下，agent信息现在直接通过agent参数传递，
    这个函数主要用于向后兼容旧的handoff模式。

    Looks for patterns like:
    - "Handoff from 主控制器 to [agent_name]"
    - "handoff to [agent_name]"

    Args:
        logs (str, optional): The logs content to parse

    Returns:
        str: The extracted agent name, or None if not found
    """
    if not logs:
        return None

    # Pattern 1: "Handoff from X to Y" - extract Y
    handoff_pattern = r'Handoff from .+ to (.+)'
    match = re.search(handoff_pattern, logs, re.IGNORECASE)
    if match:
        agent_name = match.group(1).strip()
        logger.debug(f"Extracted agent name from handoff pattern: {agent_name}")
        return agent_name

    # Pattern 2: "handoff to X" - extract X
    handoff_to_pattern = r'handoff to (.+)'
    match = re.search(handoff_to_pattern, logs, re.IGNORECASE)
    if match:
        agent_name = match.group(1).strip()
        logger.debug(f"Extracted agent name from handoff_to pattern: {agent_name}")
        return agent_name

    # Default to None if no handoff found
    return None

def save_message(username: str, email: str, conversation_id: str, role: str, content: str,
                timestamp: Optional[int] = None, logs: Optional[str] = None,
                output_as_input: Optional[str] = None, agent: Optional[str] = None, 
                resource_url: Optional[str] = None, time_spend: Optional[int] = None) -> bool:
    """
    Save a single chatbot message to the MySQL database.

    Args:
        username (str): The username of the message sender
        email (str): The email of the message sender
        conversation_id (str): The ID of the conversation
        role (str): The role of the message sender ('user', 'assistant', 'prompt', 'error')
        content (str): The content of the message
        timestamp (int, optional): The timestamp of the message in milliseconds. If None, current time is used.
        logs (str, optional): Additional logs associated with the message.
        output_as_input (str, optional): Structured output intended as input for the next turn (JSON format).
        agent (str, optional): The name of the agent that processed this message. If None, will be extracted from logs.
        resource_url (str, optional): Resource URLs (images, documents, etc.), multiple URLs separated by commas.
        time_spend (int, optional): AI响应耗时(秒)，从发起Agent请求开始到AI响应完成，只对role=assistant有意义.

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    if not username or not email:
        logger.error("Username and email are required to save a message")
        return False
    if not conversation_id:
        logger.error("Conversation ID is required to save a message")
        return False
    if not role or role not in ('user', 'assistant', 'prompt', 'error'):
        logger.error(f"Invalid role: {role}")
        return False
    if content is None:
        logger.error("Message content cannot be None")
        return False

    # If no timestamp is provided, use current time in milliseconds
    current_timestamp = timestamp if timestamp is not None else int(datetime.now().timestamp() * 1000)

    # Extract agent name from logs if not explicitly provided
    agent_name = agent if agent is not None else extract_agent_name_from_logs(logs)

    # If still no agent name found, use default
    if agent_name is None:
        agent_name = 'master_controller_bot'

    # 对于非assistant角色的消息，time_spend应该为NULL
    if role != 'assistant':
        time_spend = None

    sql = """
        INSERT INTO chat_history (username, email, conversation_id, role, content, logs, timestamp, output_as_input, agent, resource_url, time_spend)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    values = (username, email, conversation_id, role, content, logs, current_timestamp, output_as_input, agent_name, resource_url, time_spend)

    try:
        execute_db_query(sql, values, commit=True)
        if time_spend is not None and role == 'assistant':
            logger.debug(f"Saved assistant message for user {username} ({email}) in conversation {conversation_id} with agent {agent_name}, time_spend: {time_spend}s")
        else:
            logger.debug(f"Saved message for user {username} ({email}) in conversation {conversation_id} with agent {agent_name}")
        return True
    except Error as e:
        # Error already logged
        return False
    except Exception as e:
        logger.error(f"Unexpected error saving message: {e}", exc_info=True)
        return False



def load_history(username: str, email: str, limit: int = 20, offset: int = 0) -> Dict[str, List[Dict[str, Any]]]:
    """
    分页加载用户的聊天记录，按对话分组，并按每个对话的最新消息时间降序排列对话。

    实现逻辑:
    1. 查找用户在指定分页范围内的对话ID。
       - 按 `conversation_id` 分组。
       - 计算每个对话的最大时间戳 `MAX(timestamp)`。
       - 按 `MAX(timestamp)` 降序排序。
       - 应用 `LIMIT` 和 `OFFSET`。
    2. 获取这些选定对话ID的所有消息。
       - 使用 `WHERE conversation_id IN (...)`。
       - 按 `conversation_id` 和 `timestamp` 升序排序，以确保对话内消息有序。
    3. 将获取的消息按 `conversation_id` 组织成字典结构。
    4. 按照步骤1中获取的对话ID顺序，构建最终返回的有序字典。
    """
    if not username or not email:
        logger.error("加载历史记录需要用户名和邮箱")
        return {}

    history: Dict[str, List[Dict[str, Any]]] = {}
    conn = None
    cursor = None

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True) # 使用字典游标

        # 步骤 1: 获取分页的对话ID列表，包含 bad case 和 good case 状态
        convo_sql = """
            SELECT ch.conversation_id, MAX(ch.timestamp) as last_message_time,
                   CASE WHEN bc.conversation_id IS NOT NULL THEN 1 ELSE 0 END as is_bad_case,
                   CASE WHEN gc.conversation_id IS NOT NULL THEN 1 ELSE 0 END as is_good_case
            FROM chat_history ch
            LEFT JOIN bad_case bc ON ch.conversation_id = bc.conversation_id
            LEFT JOIN good_case gc ON ch.conversation_id = gc.conversation_id
            WHERE ch.username = %s AND ch.email = %s
            GROUP BY ch.conversation_id, bc.conversation_id, gc.conversation_id
            ORDER BY last_message_time DESC
            LIMIT %s OFFSET %s
        """
        logger.debug(f"执行对话ID查询: {convo_sql} | 参数: {(username, email, limit, offset)}")
        cursor.execute(convo_sql, (username, email, limit, offset))
        paginated_convos = cursor.fetchall()
        paginated_convo_ids = [row['conversation_id'] for row in paginated_convos]

        # 保存 bad case 和 good case 状态信息
        convo_bad_case_status = {row['conversation_id']: bool(row['is_bad_case']) for row in paginated_convos}
        convo_good_case_status = {row['conversation_id']: bool(row['is_good_case']) for row in paginated_convos}

        if not paginated_convo_ids:
            logger.info(f"用户 {username} ({email}) 在范围 offset={offset}, limit={limit} 内未找到对话")
            return {}

        # 步骤 2: 获取这些对话的所有消息，包含详细的反馈信息
        placeholders = ','.join(['%s'] * len(paginated_convo_ids))
        messages_sql = f"""
            SELECT ch.conversation_id, ch.role, ch.content, ch.logs, ch.timestamp, ch.updated_at, ch.resource_url, ch.agent, ch.time_spend, ch.is_in_process,
                   ch.time_to_first_token,
                   bc.feedback_tags as bad_feedback_tags,
                   bc.custom_feedback as bad_custom_feedback,
                   bc.feedback_submitted_at as bad_feedback_submitted_at,
                   gc.feedback_tags as good_feedback_tags,
                   gc.custom_feedback as good_custom_feedback,
                   gc.feedback_submitted_at as good_feedback_submitted_at
            FROM chat_history ch
            LEFT JOIN bad_case bc ON ch.conversation_id = bc.conversation_id
            LEFT JOIN good_case gc ON ch.conversation_id = gc.conversation_id
            WHERE ch.username = %s AND ch.email = %s AND ch.conversation_id IN ({placeholders})
            ORDER BY ch.timestamp ASC -- 按时间升序排列单个对话内的消息
        """
        query_params = [username, email] + paginated_convo_ids
        logger.debug(f"执行消息查询: {messages_sql} | 参数数量: {len(query_params)}")
        cursor.execute(messages_sql, tuple(query_params))
        rows = cursor.fetchall()

        # 步骤 3: 构建临时历史字典，包含详细的反馈信息处理
        temp_history: Dict[str, List[Dict[str, Any]]] = {}
        for row in rows:
            convo_id = row['conversation_id']
            if convo_id not in temp_history:
                temp_history[convo_id] = []

            # 处理好评反馈信息
            good_feedback = None
            if row.get('good_feedback_submitted_at'):
                good_feedback = {
                    'feedback_tags': [],
                    'custom_feedback': row.get('good_custom_feedback', ''),
                    'feedback_submitted_at': row.get('good_feedback_submitted_at')
                }
                # 解析JSON格式的标签
                if row.get('good_feedback_tags'):
                    try:
                        import json
                        good_feedback['feedback_tags'] = json.loads(row['good_feedback_tags'])
                    except (json.JSONDecodeError, TypeError):
                        good_feedback['feedback_tags'] = []

            # 处理差评反馈信息
            bad_feedback = None
            if row.get('bad_feedback_submitted_at'):
                bad_feedback = {
                    'feedback_tags': [],
                    'custom_feedback': row.get('bad_custom_feedback', ''),
                    'feedback_submitted_at': row.get('bad_feedback_submitted_at')
                }
                # 解析JSON格式的标签
                if row.get('bad_feedback_tags'):
                    try:
                        import json
                        bad_feedback['feedback_tags'] = json.loads(row['bad_feedback_tags'])
                    except (json.JSONDecodeError, TypeError):
                        bad_feedback['feedback_tags'] = []

            temp_history[convo_id].append({
                "role": row['role'],
                "content": row['content'],
                "logs": row['logs'],
                "timestamp": row['timestamp'], # 保持 BIGINT，消息创建时间
                "updated_at": row['updated_at'], # 消息完成/更新时间
                "resource_url": row['resource_url'],
                "agent": row['agent'],
                "time_spend": row['time_spend'],
                "is_in_process": row['is_in_process'],
                "time_to_first_token": row['time_to_first_token'],
                "isBadCase": convo_bad_case_status.get(convo_id, False),
                "isGoodCase": convo_good_case_status.get(convo_id, False),
                "goodCaseFeedback": good_feedback,
                "badCaseFeedback": bad_feedback
            })

        # 步骤 4: 按分页顺序构建最终历史字典
        for convo_id in paginated_convo_ids:
            if convo_id in temp_history:
                history[convo_id] = temp_history[convo_id]
            else:
                # 这通常不应该发生，除非在两个查询之间数据被删除
                logger.warning(f"在消息查询中未找到预期的对话ID {convo_id}")


        logger.info(f"为用户 {username} ({email}) 加载了 {len(history)} 个对话 (limit={limit}, offset={offset})")
        return history

    except Error as e:
        logger.error(f"从MySQL加载分页历史记录失败，用户 {username} ({email}): {e}", exc_info=True)
        return {} # 出错时返回空字典
    except Exception as e:
        logger.error(f"加载分页历史记录时发生意外错误，用户 {username} ({email}): {e}", exc_info=True)
        return {}
    finally:
        if cursor:
            cursor.close()
        if conn and conn.is_connected():
            conn.close()

def load_conversation(conversation_id: str, username: Optional[str] = None, email: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    从 MySQL 加载指定对话的所有消息，包含 bad case 和 good case 状态。

    Args:
        conversation_id (str): 要加载的对话ID
        username (str, optional): 用户名过滤条件
        email (str, optional): 邮箱过滤条件

    Returns:
        List[Dict[str, Any]]: 对话消息列表，包含 is_bad_case 和 is_good_case 状态
    """
    if not conversation_id:
        logger.warning("对话ID是必需的")
        return []

    # 构建 SQL - 包含 bad case 和 good case 状态以及反馈信息
    sql = """
        SELECT ch.id, ch.conversation_id, ch.role, ch.content, ch.logs, ch.timestamp, ch.updated_at, ch.output_as_input, ch.resource_url, ch.time_to_first_token,
               CASE WHEN bc.conversation_id IS NOT NULL THEN 1 ELSE 0 END as is_bad_case,
               CASE WHEN gc.conversation_id IS NOT NULL THEN 1 ELSE 0 END as is_good_case,
               bc.feedback_tags as bad_feedback_tags,
               bc.custom_feedback as bad_custom_feedback,
               bc.feedback_submitted_at as bad_feedback_submitted_at,
               gc.feedback_tags as good_feedback_tags,
               gc.custom_feedback as good_custom_feedback,
               gc.feedback_submitted_at as good_feedback_submitted_at
        FROM chat_history ch
        LEFT JOIN bad_case bc ON ch.conversation_id = bc.conversation_id
        LEFT JOIN good_case gc ON ch.conversation_id = gc.conversation_id
        WHERE ch.conversation_id = %s
    """
    values = (conversation_id,)

    # 如果提供了用户名和邮箱，添加过滤条件
    if username and email:
        sql += " AND ch.username = %s AND ch.email = %s"
        values = (conversation_id, username, email, )

    sql += " ORDER BY ch.timestamp ASC"

    try:
        results = execute_db_query(sql, values, fetch='all')
        if not results:
            return []

        # 处理反馈信息
        processed_results = []
        for row in results:
            # 复制原始数据
            processed_row = dict(row)

            # 处理负面反馈
            bad_feedback = None
            if row.get('bad_feedback_submitted_at'):
                bad_feedback = {
                    'feedback_tags': [],
                    'custom_feedback': row.get('bad_custom_feedback', ''),
                    'feedback_submitted_at': row.get('bad_feedback_submitted_at')
                }
                # 解析JSON格式的标签
                if row.get('bad_feedback_tags'):
                    try:
                        bad_feedback['feedback_tags'] = json.loads(row['bad_feedback_tags'])
                    except (json.JSONDecodeError, TypeError):
                        bad_feedback['feedback_tags'] = []

            # 处理正面反馈
            good_feedback = None
            if row.get('good_feedback_submitted_at'):
                good_feedback = {
                    'feedback_tags': [],
                    'custom_feedback': row.get('good_custom_feedback', ''),
                    'feedback_submitted_at': row.get('good_feedback_submitted_at')
                }
                # 解析JSON格式的标签
                if row.get('good_feedback_tags'):
                    try:
                        good_feedback['feedback_tags'] = json.loads(row['good_feedback_tags'])
                    except (json.JSONDecodeError, TypeError):
                        good_feedback['feedback_tags'] = []

            # 添加处理后的反馈信息
            processed_row['badCaseFeedback'] = bad_feedback
            processed_row['goodCaseFeedback'] = good_feedback
            processed_row['isBadCase'] = bool(row.get('is_bad_case'))
            processed_row['isGoodCase'] = bool(row.get('is_good_case'))

            # 移除原始的反馈字段以保持数据清洁
            for key in ['bad_feedback_tags', 'bad_custom_feedback', 'bad_feedback_submitted_at',
                       'good_feedback_tags', 'good_custom_feedback', 'good_feedback_submitted_at']:
                processed_row.pop(key, None)

            processed_results.append(processed_row)

        return processed_results
    except Error as e:
        logger.exception(f"加载对话 {conversation_id} 时发生数据库错误: {e}", exc_info=True)
        return []
    except Exception as e:
        logger.exception(f"加载对话 {conversation_id} 时发生意外错误: {e}", exc_info=True)
        return []

def delete_conversation(username: str, email: str, conversation_id: str) -> bool:
    """
    Delete all messages in a specific conversation from MySQL.

    Args:
        username (str): The username of the conversation owner
        email (str): The email of the conversation owner
        conversation_id (str): The ID of the conversation to delete

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    if not username or not email or not conversation_id:
        logger.warning("Username, email, and conversation_id are required to delete a conversation")
        return False

    sql = """
        DELETE FROM chat_history
        WHERE username = %s AND email = %s AND conversation_id = %s
    """
    values = (username, email, conversation_id)

    try:
        affected_rows = execute_db_query(sql, values, fetch='count', commit=True)
        logger.info(f"Deleted conversation {conversation_id} for user {username} ({email}), {affected_rows} messages removed")
        return True
    except Error as e:
        # Error already logged
        return False
    except Exception as e:
        logger.error(f"Unexpected error deleting conversation {conversation_id} for user {username} ({email}): {e}", exc_info=True)
        return False

def get_history_conversation_count(username: str, email: str) -> int:
    """
    Get the count of unique conversations for a specific user from MySQL.

    Args:
        username (str): The username to get count for
        email (str): The email to get count for

    Returns:
        int: The count of unique conversations
    """
    if not username or not email:
        logger.warning("Username and email are required to get conversation count")
        return 0

    sql = """
        SELECT COUNT(DISTINCT conversation_id) as count
        FROM chat_history
        WHERE username = %s AND email = %s
    """
    values = (username, email)

    try:
        result = execute_db_query(sql, values, fetch='one')
        return result.get('count', 0) if result else 0
    except Error as e:
        # Error already logged
        return 0
    except Exception as e:
        logger.error(f"Unexpected error getting conversation count for user {username} ({email}): {e}", exc_info=True)
        return 0


def check_conversation_owner(conversation_id: str) -> Optional[Dict[str, str]]:
    """
    检查对话的所有者信息

    Args:
        conversation_id (str): 对话ID

    Returns:
        Optional[Dict[str, str]]: 包含 username 和 email 的字典，如果对话不存在则返回 None
    """
    if not conversation_id:
        logger.warning("对话ID不能为空")
        return None

    sql = """
        SELECT username, email
        FROM chat_history
        WHERE conversation_id = %s
        ORDER BY timestamp ASC
        LIMIT 1
    """
    values = (conversation_id,)

    try:
        result = execute_db_query(sql, values, fetch='one')
        if not result:
            logger.warning(f"对话 {conversation_id} 不存在")
            return None

        return {
            'username': result.get('username'),
            'email': result.get('email')
        }
    except Error as e:
        # Error already logged
        return None
    except Exception as e:
        logger.error(f"检查对话所有者时发生意外错误: {e}", exc_info=True)
        return None


def get_user_latest_queries_from_db(user_email: str, limit: int = 10) -> List[str]:
    """
    获取指定用户最近的查询消息内容列表，优先获取good case和已修复的bad case，
    按 email 和 conversation_id 分组，将 content 使用 GROUP_CONCAT 聚合，并返回前 limit 条。

    Args:
        user_email (str): 用户邮箱
        limit (int): 最大返回数量，默认10

    Returns:
        List[str]: 聚合后的用户查询消息内容列表
    """
    if not user_email:
        logger.warning("用户邮箱不能为空")
        return []

    sql = """
        SELECT GROUP_CONCAT(ch.content ORDER BY ch.timestamp ASC SEPARATOR ' ') AS aggregated_content,
               CASE
                   WHEN gc.conversation_id IS NOT NULL THEN 1  -- good case 优先级最高
                   WHEN bc.conversation_id IS NOT NULL AND bc.repair_status = 1 THEN 2  -- 已修复的 bad case 次之
                   ELSE 3  -- 其他情况优先级最低
               END as priority_level
        FROM chat_history ch
        LEFT JOIN good_case gc ON ch.conversation_id = gc.conversation_id
        LEFT JOIN bad_case bc ON ch.conversation_id = bc.conversation_id
        WHERE ch.email = %s AND ch.role = 'user' AND ch.content IS NOT NULL AND ch.content != ''
        GROUP BY ch.email, ch.conversation_id, gc.conversation_id, bc.conversation_id, bc.repair_status
        ORDER BY priority_level ASC, MAX(ch.timestamp) DESC
        LIMIT %s
    """
    values = (user_email, limit)

    try:
        results = execute_db_query(sql, values, fetch='all')
        if not results:
            logger.info(f"用户 {user_email} 没有找到任何查询记录")
            return []

        # 提取聚合后的消息内容
        queries = [row['aggregated_content'] for row in results if row['aggregated_content']]
        logger.info(f"为用户 {user_email} 获取到 {len(queries)} 条聚合查询记录（优先good case和已修复bad case）")
        return queries

    except Error as e:
        logger.error(f"获取用户 {user_email} 查询记录时发生数据库错误: {e}", exc_info=True)
        return []
    except Exception as e:
        logger.error(f"获取用户 {user_email} 查询记录时发生意外错误: {e}", exc_info=True)
        return []


def get_other_users_latest_queries_from_db(current_user_email: str, limit: int = 10) -> List[str]:
    """
    获取除当前用户外其他用户最近的查询消息内容列表，优先获取good case和已修复的bad case，
    按 email 和 conversation_id 分组，将 content 使用 GROUP_CONCAT 聚合，并返回前 limit 条。

    Args:
        current_user_email (str): 当前用户邮箱（将被排除）
        limit (int): 最大返回数量，默认10

    Returns:
        List[str]: 聚合后的其他用户查询消息内容列表
    """
    if not current_user_email:
        logger.warning("当前用户邮箱不能为空")
        return []

    sql = """
        SELECT GROUP_CONCAT(ch.content ORDER BY ch.timestamp ASC SEPARATOR ' ') AS aggregated_content,
               CASE
                   WHEN gc.conversation_id IS NOT NULL THEN 1  -- good case 优先级最高
                   WHEN bc.conversation_id IS NOT NULL AND bc.repair_status = 1 THEN 2  -- 已修复的 bad case 次之
                   ELSE 3  -- 其他情况优先级最低
               END as priority_level
        FROM chat_history ch
        LEFT JOIN good_case gc ON ch.conversation_id = gc.conversation_id
        LEFT JOIN bad_case bc ON ch.conversation_id = bc.conversation_id
        WHERE ch.email != %s AND ch.role = 'user' AND ch.content IS NOT NULL AND ch.content != ''
        GROUP BY ch.email, ch.conversation_id, gc.conversation_id, bc.conversation_id, bc.repair_status
        ORDER BY priority_level ASC, MAX(ch.timestamp) DESC
        LIMIT %s
    """
    values = (current_user_email, limit)

    try:
        results = execute_db_query(sql, values, fetch='all')
        if not results:
            logger.info(f"除用户 {current_user_email} 外没有找到其他用户的查询记录")
            return []

        # 提取聚合后的消息内容
        queries = [row['aggregated_content'] for row in results if row['aggregated_content']]
        logger.info(f"为用户 {current_user_email} 获取到 {len(queries)} 条其他用户聚合查询记录（优先good case和已修复bad case）")
        return queries

    except Error as e:
        logger.error(f"获取其他用户查询记录时发生数据库错误（当前用户: {current_user_email}): {e}", exc_info=True)
        return []
    except Exception as e:
        logger.error(f"获取其他用户查询记录时发生意外错误（当前用户: {current_user_email}): {e}", exc_info=True)
        return []


def create_streaming_assistant_message(username: str, email: str, conversation_id: str, 
                                     timestamp: Optional[int] = None, agent: Optional[str] = None) -> Optional[int]:
    """
    创建一条流式assistant消息记录，初始内容为空，is_in_process=1
    
    Args:
        username (str): 用户名
        email (str): 用户邮箱
        conversation_id (str): 对话ID
        timestamp (int, optional): 时间戳，如果为None则使用当前时间
        agent (str, optional): agent名称，如果为None则使用默认值
        
    Returns:
        Optional[int]: 创建的消息记录ID，失败时返回None
    """
    if not username or not email or not conversation_id:
        logger.error("创建流式assistant消息需要用户名、邮箱和对话ID")
        return None
        
    # 如果没有提供时间戳，使用当前时间
    current_timestamp = timestamp if timestamp is not None else int(datetime.now().timestamp() * 1000)
    
    # 如果没有提供agent名称，使用默认值
    agent_name = agent if agent is not None else 'master_controller_bot'
    
    sql = """
        INSERT INTO chat_history (username, email, conversation_id, role, content, timestamp, agent, is_in_process)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
    """
    values = (username, email, conversation_id, 'assistant', '', current_timestamp, agent_name, 1)
    
    try:
        result = execute_db_query(sql, values, commit=True, fetch='lastrowid')
        message_id = result if result else None
        if message_id:
            logger.debug(f"创建流式assistant消息记录成功，ID: {message_id} (Convo ID: {conversation_id})")
        return message_id
    except Error as e:
        logger.error(f"创建流式assistant消息记录失败: {e}", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"创建流式assistant消息记录时发生意外错误: {e}", exc_info=True)
        return None


def update_streaming_assistant_message(message_id: int, content: str, logs: Optional[str] = None,
                                     agent: Optional[str] = None, output_as_input: Optional[str] = None,
                                     time_spend: Optional[int] = None, is_completed: bool = False) -> bool:
    """
    更新流式assistant消息的内容、日志和其他字段

    Args:
        message_id (int): 消息记录ID
        content (str): 更新的消息内容
        logs (str, optional): 执行日志
        agent (str, optional): agent名称
        output_as_input (str, optional): 结构化输出
        time_spend (int, optional): 响应耗时(秒)
        is_completed (bool): 是否已完成

    Returns:
        bool: 更新成功返回True，失败返回False
    """
    if not message_id:
        logger.error("更新流式assistant消息需要消息ID")
        return False

    if content is None:
        logger.error("更新流式assistant消息的内容不能为None")
        return False

    # 检查是否为首次更新（内容从空变为非空），如果是则计算time_to_first_token
    time_to_first_token = None
    if content.strip():  # 只有当内容不为空时才检查是否为首次更新
        try:
            # 查询当前消息的状态
            check_sql = """
                SELECT content, created_at, time_to_first_token 
                FROM chat_history 
                WHERE id = %s AND role = 'assistant'
            """
            result = execute_db_query(check_sql, (message_id,), fetch='one')
            
            if result:
                current_content = result.get('content', '')
                created_at = result.get('created_at')
                existing_time_to_first_token = result.get('time_to_first_token')
                
                # 如果当前内容为空且time_to_first_token还未设置（为0或None），说明这是首次更新
                if (not current_content or current_content.strip() == '') and (existing_time_to_first_token is None or existing_time_to_first_token == 0):
                    if created_at:
                        # 计算首次响应时间（秒）
                        current_time = datetime.now()
                        time_diff = (current_time - created_at).total_seconds()
                        time_to_first_token = int(time_diff)
                        logger.debug(f"首次更新assistant消息 (ID: {message_id})，time_to_first_token: {time_to_first_token}秒")
                    
        except Exception as e:
            logger.warning(f"计算time_to_first_token时出错: {e}")
            # 不影响主要的更新流程，继续执行

    # 构建动态更新SQL - 简化参数处理，移除不必要的类型检查
    update_fields = ["content = %s"]
    values = [content]

    if logs is not None:
        update_fields.append("logs = %s")
        values.append(logs)

    if agent is not None:
        update_fields.append("agent = %s")
        values.append(agent)

    if output_as_input is not None:
        update_fields.append("output_as_input = %s")
        values.append(output_as_input)

    if time_spend is not None:
        update_fields.append("time_spend = %s")
        values.append(time_spend)

    # 如果计算出了time_to_first_token，则更新该字段
    if time_to_first_token is not None:
        update_fields.append("time_to_first_token = %s")
        values.append(time_to_first_token)

    # 设置is_in_process字段
    is_in_process = 0 if is_completed else 1
    update_fields.append("is_in_process = %s")
    values.append(is_in_process)

    # 添加更新时间
    update_fields.append("updated_at = CURRENT_TIMESTAMP")

    sql = f"""
        UPDATE chat_history
        SET {', '.join(update_fields)}
        WHERE id = %s AND role = 'assistant'
    """
    values.append(message_id)



    try:
        affected_rows = execute_db_query(sql, tuple(values), commit=True, fetch='count')
        if affected_rows > 0:
            status = "完成" if is_completed else "更新"
            logger.debug(f"流式assistant消息{status}成功，ID: {message_id}，内容长度: {len(content)}")
            return True
        else:
            logger.warning(f"未找到要更新的assistant消息记录，ID: {message_id}")
            return False
    except Error as e:
        logger.error(f"更新流式assistant消息失败，ID: {message_id}: {e}", exc_info=True)
        return False
    except Exception as e:
        logger.error(f"更新流式assistant消息时发生意外错误，ID: {message_id}: {e}", exc_info=True)
        return False


def get_streaming_assistant_message_id(username: str, email: str, conversation_id: str) -> Optional[int]:
    """
    获取指定对话中最新的正在处理中的assistant消息ID
    
    Args:
        username (str): 用户名
        email (str): 用户邮箱
        conversation_id (str): 对话ID
        
    Returns:
        Optional[int]: 消息ID，如果没有找到则返回None
    """
    if not username or not email or not conversation_id:
        logger.error("获取流式assistant消息ID需要用户名、邮箱和对话ID")
        return None
    
    sql = """
        SELECT id FROM chat_history 
        WHERE username = %s AND email = %s AND conversation_id = %s 
        AND role = 'assistant' AND is_in_process = 1
        ORDER BY timestamp DESC 
        LIMIT 1
    """
    values = (username, email, conversation_id)
    
    try:
        result = execute_db_query(sql, values, fetch='one')
        if result:
            message_id = result.get('id')
            logger.debug(f"找到正在处理中的assistant消息ID: {message_id} (Convo ID: {conversation_id})")
            return message_id
        else:
            logger.debug(f"未找到正在处理中的assistant消息 (Convo ID: {conversation_id})")
            return None
    except Error as e:
        logger.error(f"获取流式assistant消息ID失败: {e}", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"获取流式assistant消息ID时发生意外错误: {e}", exc_info=True)
        return None
